import React from "react";
import { Input as HeroUIInput } from "@heroui/react";
import { cn } from "../../../lib/utils";

/**
 * Input Component - HeroUI Implementation
 *
 * A form input component for collecting user input.
 * Compatible with shadcn/ui Input API for easy migration.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.type] - Input type (text, password, email, etc.)
 * @returns {React.ReactElement} - Input component
 */
const Input = React.forwardRef(({ className, type = "text", ...props }, ref) => {
  return (
    <HeroUIInput
      ref={ref}
      type={type}
      variant="bordered"
      className={cn("", className)}
      classNames={{
        input: "text-sm text-foreground placeholder:text-default-600",
        inputWrapper: "border-input bg-background hover:border-ring focus-within:border-ring",
      }}
      {...props}
    />
  );
});

Input.displayName = "Input";

export { Input };
