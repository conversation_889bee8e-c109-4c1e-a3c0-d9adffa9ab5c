import React from "react";
import { Textarea as HeroUITextar<PERSON> } from "@heroui/react";
import { cn } from "../../../lib/utils";

/**
 * Textarea Component - HeroUI Implementation
 *
 * A textarea component for multi-line text input.
 * Compatible with shadcn/ui Textarea API for easy migration.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @returns {React.ReactElement} - Textarea component
 */
const Textarea = React.forwardRef(({ className, ...props }, ref) => {
  return (
    <HeroUITextarea
      ref={ref}
      variant="bordered"
      className={cn("", className)}
      classNames={{
        input: "text-sm min-h-[80px] text-foreground placeholder:text-default-500",
        inputWrapper: "border-input bg-background hover:border-ring focus-within:border-ring",
      }}
      {...props}
    />
  );
});

Textarea.displayName = "Textarea";

export { Textarea };
