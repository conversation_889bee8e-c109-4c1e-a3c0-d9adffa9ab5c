/* React DatePicker Styles */
@import 'react-datepicker/dist/react-datepicker.css';

/* Minimal base styles - let HeroUI handle everything else */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden; /* Prevent horizontal scroll but allow vertical */
  overflow-y: auto; /* Allow vertical scrolling when needed */
  max-width: 100vw; /* Prevent body from exceeding viewport width */
}

/* Ensure html handles overflow properly */
html {
  overflow-x: hidden; /* Prevent horizontal scroll */
  overflow-y: auto; /* Allow vertical scrolling */
  height: 100%;
  width: 100%;
  max-width: 100vw; /* Prevent html from exceeding viewport width */
}

/* Ensure root container doesn't overflow */
#root {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Fix screen reader elements to not cause overflow */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Content scrolling styles */
.content-scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin; /* Show thin scrollbars in content areas */
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.content-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.content-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.content-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Project Detail Page Styles - Maximum Specificity to Override All Frameworks */
body .project-detail-container,
div.project-detail-container,
.project-detail-container {
  min-height: 100vh !important;
  background-color: #f8f9fa !important;
  padding: 20px !important;
  color: #333 !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
  z-index: 1 !important;
}

.project-header {
  background: white !important;
  border-radius: 8px !important;
  padding: 24px !important;
  margin-bottom: 24px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  flex-wrap: wrap !important;
  gap: 16px !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.project-title-section {
  flex: 1 !important;
  min-width: 300px !important;
  display: block !important;
}

.project-title-container {
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
}

.project-thumbnail {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.project-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-title {
  font-size: 2rem !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
  color: #1a1a1a !important;
  display: block !important;
}

.project-meta {
  display: flex !important;
  gap: 12px !important;
  align-items: center !important;
}

.project-type, .project-status {
  padding: 4px 12px !important;
  border-radius: 16px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  display: inline-block !important;
}

.project-type {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
}

.project-status {
  background-color: #e8f5e8 !important;
  color: #2e7d32 !important;
}

.project-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: flex-start;
}

.project-actions .btn {
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.project-actions .btn-primary {
  background-color: #1976d2;
  color: white;
}

.project-actions .btn-secondary {
  background-color: #757575;
  color: white;
}

.project-actions .btn-success {
  background-color: #388e3c;
  color: white;
}

.project-actions .btn-info {
  background-color: #0288d1;
  color: white;
}

.project-actions .btn-dark {
  background-color: #424242;
  color: white;
}

.project-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Project Content Area - High Specificity */
.project-content {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  overflow: hidden !important;
  display: block !important;
  width: 100% !important;
  min-height: 400px !important;
}

.project-tabs {
  display: flex !important;
  border-bottom: 1px solid #e0e0e0 !important;
  background-color: #fafafa !important;
  margin-bottom: 0 !important;
  width: 100% !important;
}

.tab-button {
  padding: 16px 24px !important;
  cursor: pointer !important;
  border: none !important;
  background: none !important;
  font-weight: 500 !important;
  color: #666 !important;
  transition: all 0.2s ease !important;
  border-bottom: 3px solid transparent !important;
  display: inline-block !important;
}

.tab-button:hover {
  background-color: #f0f0f0 !important;
  color: #333 !important;
}

.tab-button.active {
  color: #1976d2 !important;
  border-bottom-color: #1976d2 !important;
  background-color: white !important;
}

.project-tab-content {
  padding: 24px !important;
  min-height: 400px !important;
  display: block !important;
}

/* Project Description */
.project-description {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  color: #555;
  line-height: 1.6;
}

/* Project Overview Section - High Specificity */
.project-overview {
  display: flex !important;
  flex-direction: column !important;
  gap: 32px !important;
  padding: 24px !important;
  min-height: 400px !important;
}

.overview-section {
  background: #f8f9fa !important;
  border-radius: 8px !important;
  padding: 24px !important;
  display: block !important;
  margin-bottom: 20px !important;
}

.overview-section h3 {
  margin: 0 0 20px 0 !important;
  color: #1a1a1a !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  display: block !important;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #1976d2;
}

.detail-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.detail-value {
  font-size: 1rem;
  color: #1a1a1a;
  font-weight: 600;
}

/* Royalty Model Styles */
.royalty-model {
  background: white;
  padding: 20px;
  border-radius: 6px;
}

.model-type {
  margin-bottom: 16px;
  font-size: 1rem;
}

.model-weights {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.weight-item {
  background: #f0f0f0;
  padding: 12px 16px;
  border-radius: 6px;
  text-align: center;
  min-width: 80px;
}

.weight-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 4px;
}

.weight-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1976d2;
}

.learn-more-button {
  background: #1976d2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.learn-more-button:hover {
  background: #1565c0;
}

.royalty-explanation {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #1976d2;
}

.explanation-text {
  margin-bottom: 20px;
}

.explanation-text p {
  margin-bottom: 12px;
  line-height: 1.6;
}

.explanation-text ul {
  margin-left: 20px;
  margin-bottom: 12px;
}

.explanation-text li {
  margin-bottom: 8px;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 6px;
  text-align: center;
  border-top: 4px solid #1976d2;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

/* Project Not Found */
.project-not-found {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  margin: 40px auto;
  max-width: 500px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.project-not-found h2 {
  color: #d32f2f;
  margin-bottom: 16px;
}

.project-not-found p {
  color: #666;
  margin-bottom: 24px;
}

/* Project Tab Content Sections */
.project-contributions, .project-revenue, .project-milestones,
.project-activity, .project-analytics {
  min-height: 400px;
}

.contributions-header, .revenue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.contributions-header h3, .revenue-header h3 {
  margin: 0;
  color: #1a1a1a;
  font-size: 1.25rem;
  font-weight: 600;
}

.section-description {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}

/* Revenue Tab Styles */
.revenue-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.revenue-tab-button {
  padding: 12px 20px;
  border: none;
  background: none;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.revenue-tab-button:hover {
  color: #333;
  background-color: #f0f0f0;
}

.revenue-tab-button.active {
  color: #1976d2;
  border-bottom-color: #1976d2;
}

.revenue-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}

.revenue-preview-link {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  color: #1976d2;
  text-decoration: none;
  font-size: 1.125rem;
  font-weight: 500;
  padding: 16px 24px;
  border: 2px solid #1976d2;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.revenue-preview-link:hover {
  background-color: #1976d2;
  color: white;
}

/* Progress Bar Styles */
.progress {
  background-color: #e0e0e0;
  border-radius: 4px;
  height: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

/* Interactive Example Styles */
.interactive-example {
  margin-top: 24px;
}

.interactive-example h5 {
  margin-bottom: 16px;
  color: #1a1a1a;
}

.example-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 20px;
}

.example-table h6, .example-results h6 {
  margin-bottom: 12px;
  color: #1a1a1a;
  font-size: 1rem;
}

.example-table table, .example-results table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.example-table th, .example-results th,
.example-table td, .example-results td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.example-table th, .example-results th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #1a1a1a;
}

.example-table td, .example-results td {
  color: #555;
}

/* Model Visualization */
.model-visualization {
  margin-top: 20px;
}

.pie-chart-container {
  background: white;
  padding: 20px;
  border-radius: 6px;
  text-align: center;
}

.pie-chart-container h5 {
  margin-bottom: 16px;
  color: #1a1a1a;
}

/* Responsive Design */
@media (max-width: 768px) {
  .project-header {
    flex-direction: column;
    align-items: stretch;
  }

  .project-title-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .project-actions {
    justify-content: flex-start;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .model-weights {
    flex-direction: column;
  }

  .example-data {
    grid-template-columns: 1fr;
  }

  .project-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1;
    min-width: 120px;
  }
}

/* Navigation areas - hide scrollbars completely */
.navigation-area {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navigation-area::-webkit-scrollbar {
  display: none;
}

/* Allow scrolling within canvas content when in content mode */
.canvas-content {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.canvas-content::-webkit-scrollbar {
  width: 6px;
}

.canvas-content::-webkit-scrollbar-track {
  background: transparent;
}

.canvas-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.canvas-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.pointer {
  cursor: pointer;
}

/* Prevent browser extension interference with forms */
input[data-lpignore="true"],
textarea[data-lpignore="true"],
select[data-lpignore="true"] {
  -webkit-text-security: none !important;
  background-image: none !important;
  background-color: transparent !important;
}

/* Prevent password manager icons from appearing */
input[data-form-type="financial"]::-webkit-credentials-auto-fill-button,
input[data-form-type="reference"]::-webkit-credentials-auto-fill-button,
input[data-form-type="description"]::-webkit-credentials-auto-fill-button {
  display: none !important;
  visibility: hidden !important;
}

/* Prevent large text elements from overflowing */
.text-6xl, .text-5xl, .text-4xl, .text-3xl {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Ensure all containers respect viewport width */
* {
  box-sizing: border-box;
}

div, section, main, article, aside, header, footer, nav {
  max-width: 100%;
  overflow-wrap: break-word;
}

/* Fix dropdown and select elements */
select, .dropdown-menu, [role="listbox"], [role="menu"] {
  max-width: 100vw !important;
  overflow-x: hidden !important;
}

/* Fix dropdown containers that are positioned absolutely */
div[role="listbox"], div[role="menu"], .dropdown-content {
  max-width: calc(100vw - 20px) !important;
  overflow-x: hidden !important;
  word-wrap: break-word !important;
}

/* Fix any absolutely positioned divs that might be dropdowns */
div[style*="position: absolute"] {
  max-width: calc(100vw - 20px) !important;
  overflow-x: hidden !important;
}

/* Ensure text content in dropdowns wraps properly */
div[role="listbox"] span, div[role="menu"] span, .dropdown-content span {
  max-width: 100% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  white-space: normal !important;
}

/* Fix text-center containers that might overflow */
.text-center {
  max-width: 100%;
  overflow-wrap: break-word;
}

/* Container width fixes */
.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
  max-width: 100vw !important;
  overflow-x: hidden !important;
}

/* Ensure full width elements don't overflow */
.w-full {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Error boundary styling */
.error-boundary-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.error-container {
  max-width: 600px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.error-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.error-container p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.error-details {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
  text-align: left;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.error-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
  max-width: 100%;
}

.error-reload-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.error-reload-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* React DatePicker custom styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.react-datepicker__input-container input:focus {
  outline: none;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

.react-datepicker__input-container input::placeholder {
  color: hsl(var(--muted-foreground));
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.react-datepicker__header {
  background: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
  border-radius: 0.5rem 0.5rem 0 0;
}

.react-datepicker__current-month {
  color: hsl(var(--foreground));
  font-weight: 600;
}

.react-datepicker__day-name {
  color: hsl(var(--muted-foreground));
  font-weight: 500;
}

.react-datepicker__day {
  color: hsl(var(--foreground));
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.react-datepicker__day:hover {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.react-datepicker__day--selected {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.react-datepicker__day--selected:hover {
  background: hsl(var(--primary) / 0.8);
}

.react-datepicker__day--today {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 600;
}

.react-datepicker__navigation {
  background: transparent;
  border: none;
  color: hsl(var(--foreground));
}

.react-datepicker__navigation:hover {
  background: hsl(var(--accent));
  border-radius: 0.25rem;
}

.react-datepicker__navigation--previous {
  left: 0.5rem;
}

.react-datepicker__navigation--next {
  right: 0.5rem;
}

/* ===== WIZARD COMPONENT STYLING FIXES ===== */

/* Wizard step content container */
.wizard-step-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

/* Wizard form styling improvements */
.wizard-form {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Wizard card styling */
.wizard-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Wizard section styling */
.wizard-section {
  padding: 1.5rem;
  border-bottom: 1px solid hsl(var(--border));
}

.wizard-section:last-child {
  border-bottom: none;
}

/* Wizard form field spacing */
.wizard-form-field {
  margin-bottom: 1.5rem;
}

.wizard-form-field:last-child {
  margin-bottom: 0;
}

/* Wizard button container */
.wizard-button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / 0.3);
}

/* Responsive wizard layout */
@media (max-width: 768px) {
  .wizard-button-container {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .wizard-button-container button {
    width: 100%;
  }
  
  .wizard-section {
    padding: 1rem;
  }
}

/* Wizard grid improvements */
.wizard-grid {
  display: grid;
  gap: 1.5rem;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.wizard-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.wizard-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 768px) {
  .wizard-grid-2,
  .wizard-grid-3 {
    grid-template-columns: 1fr;
  }
}

/* Wizard spacing utilities */
.wizard-space-y > * + * {
  margin-top: 1.5rem;
}

.wizard-space-y-sm > * + * {
  margin-top: 1rem;
}

.wizard-space-y-lg > * + * {
  margin-top: 2rem;
}

/* Wizard responsive text */
.wizard-text-responsive {
  font-size: clamp(0.875rem, 2vw, 1rem);
  line-height: 1.6;
}

.wizard-heading-responsive {
  font-size: clamp(1.25rem, 3vw, 1.5rem);
  font-weight: 600;
  line-height: 1.3;
}

/* Wizard form validation styling */
.wizard-form-error {
  color: hsl(var(--destructive));
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.wizard-form-error::before {
  content: "⚠️";
  font-size: 1rem;
}

/* Wizard success styling */
.wizard-form-success {
  color: hsl(var(--success));
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.wizard-form-success::before {
  content: "✅";
  font-size: 1rem;
}

/* Wizard loading states */
.wizard-loading {
  opacity: 0.6;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.wizard-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: hsl(var(--background) / 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: inherit;
}

/* Wizard animation improvements */
.wizard-fade-in {
  animation: wizardFadeIn 0.3s ease-out;
}

@keyframes wizardFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wizard-slide-in {
  animation: wizardSlideIn 0.4s ease-out;
}

@keyframes wizardSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Wizard focus management */
.wizard-focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* Wizard accessibility improvements */
.wizard-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Wizard high contrast mode support */
@media (prefers-contrast: high) {
  .wizard-card {
    border-width: 2px;
  }
  
  .wizard-button-container {
    border-top-width: 2px;
  }
  
  .wizard-section {
    border-bottom-width: 2px;
  }
}

/* Wizard reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .wizard-fade-in,
  .wizard-slide-in {
    animation: none;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== STUDIO CREATION WIZARD SPECIFIC STYLING ===== */

.studio-creation-wizard {
  min-height: 100vh;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.05) 0%, hsl(var(--secondary) / 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.studio-creation-card {
  width: 100%;
  max-width: 48rem;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 1.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.studio-creation-header {
  text-align: center;
  padding: 2rem 1.5rem;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--secondary) / 0.1) 100%);
  border-bottom: 1px solid hsl(var(--border));
}

.studio-creation-form {
  padding: 2rem;
}

.studio-creation-section {
  margin-bottom: 2rem;
}

.studio-creation-section:last-child {
  margin-bottom: 0;
}

.studio-creation-section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid hsl(var(--primary) / 0.2);
}

.studio-creation-section-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  background: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.studio-creation-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.studio-creation-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: hsl(var(--muted) / 0.3);
  border-top: 1px solid hsl(var(--border));
}

@media (max-width: 768px) {
  .studio-creation-wizard {
    padding: 0.5rem;
  }
  
  .studio-creation-card {
    border-radius: 1rem;
  }
  
  .studio-creation-form {
    padding: 1.5rem;
  }
  
  .studio-creation-actions {
    flex-direction: column;
    padding: 1rem 1.5rem;
  }
  
  .studio-creation-actions button {
    width: 100%;
  }
}

/* ===== GENERAL SITE STYLING IMPROVEMENTS ===== */

/* Improved form styling */
.form-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Ensure all form inputs have proper text contrast */
input, textarea, select {
  color: hsl(var(--foreground)) !important;
}

input::placeholder, textarea::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7;
}

/* HeroUI specific input text contrast fixes */
[data-slot="input"] {
  color: hsl(var(--foreground)) !important;
}

[data-slot="input"]::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7;
}

/* Specific fixes for white/light backgrounds */
.bg-white input, .bg-white textarea, .bg-white select,
.bg-background input, .bg-background textarea, .bg-background select,
[style*="background: white"] input, [style*="background: white"] textarea,
[style*="background-color: white"] input, [style*="background-color: white"] textarea {
  color: #1a1a1a !important;
}

.bg-white input::placeholder, .bg-white textarea::placeholder,
.bg-background input::placeholder, .bg-background textarea::placeholder,
[style*="background: white"] input::placeholder, [style*="background: white"] textarea::placeholder,
[style*="background-color: white"] input::placeholder, [style*="background-color: white"] textarea::placeholder {
  color: #666666 !important;
  opacity: 1;
}

/* HeroUI white background fixes */
.bg-white [data-slot="input"], .bg-background [data-slot="input"],
[style*="background: white"] [data-slot="input"], [style*="background-color: white"] [data-slot="input"] {
  color: #1a1a1a !important;
}

.bg-white [data-slot="input"]::placeholder, .bg-background [data-slot="input"]::placeholder,
[style*="background: white"] [data-slot="input"]::placeholder, [style*="background-color: white"] [data-slot="input"]::placeholder {
  color: #666666 !important;
  opacity: 1;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
}

.form-section-header {
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid hsl(var(--border));
}

.form-field-group {
  display: grid;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-field-group-2 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.form-field-group-3 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

@media (max-width: 768px) {
  .form-field-group-2,
  .form-field-group-3 {
    grid-template-columns: 1fr;
  }
}

/* Improved button styling */
.button-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.button-group-stack {
  flex-direction: column;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }
  
  .button-group button {
    width: 100%;
  }
}

/* Improved card styling */
.card-enhanced {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card-enhanced:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Improved spacing utilities */
.space-responsive {
  gap: clamp(0.5rem, 2vw, 1.5rem);
}

.padding-responsive {
  padding: clamp(1rem, 3vw, 2rem);
}

.margin-responsive {
  margin: clamp(1rem, 3vw, 2rem);
}

/* Improved responsive design */
.responsive-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 clamp(1rem, 3vw, 2rem);
  overflow-x: hidden;
}

.responsive-grid {
  display: grid;
  gap: clamp(1rem, 3vw, 2rem);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Improved accessibility */
.focus-ring {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 0.25rem;
}

.focus-ring:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Improved loading states */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: hsl(var(--background) / 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: inherit;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid hsl(var(--border));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Improved dark mode support */
@media (prefers-color-scheme: dark) {
  .card-enhanced {
    background: hsl(var(--card));
    border-color: hsl(var(--border));
  }
  
  .form-section {
    background: hsl(var(--card));
    border-color: hsl(var(--border));
  }
}

/* Improved print styles */
@media print {
  .wizard-button-container,
  .studio-creation-actions,
  .button-group {
    display: none;
  }
  
  .wizard-card,
  .studio-creation-card,
  .card-enhanced {
    box-shadow: none;
    border: 1px solid #000;
  }
}


